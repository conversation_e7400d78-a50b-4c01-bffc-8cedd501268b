[package]
name = "memprocfs"
version = "5.15.1"
edition = "2021"
description = "MemProcFS - Physical Memory Analysis Framework"
documentation = "https://docs.rs/memprocfs"
homepage = "https://github.com/ufrisk/MemProcFS"
repository = "https://github.com/ufrisk/MemProcFS"
categories = ["external-ffi-bindings"]
keywords = ["memory", "forensics", "dma", "pcileech", "memprocfs"]
readme = "README.md"
license = "AGPL-3.0-or-later"

[lib]
name = "memprocfs"
path = "src/lib_memprocfs.rs"

[dependencies]
anyhow = "1.0"
libloading = "0.8"
serde = { version = "1.0", features = ["derive"] }
