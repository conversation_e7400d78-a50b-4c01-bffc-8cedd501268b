// device_netdrv.c : implementation for network-based driver memory access
//
// This device allows MemProcFS to access physical memory through a network
// connection to a remote driver instead of using hardware DMA.
//
// (c) Your Name, 2025
//
#include "leechcore.h"
#include "leechcore_device.h"
#include "leechcore_internal.h"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#endif

//-----------------------------------------------------------------------------
// DEFINES AND TYPEDEFS
//-----------------------------------------------------------------------------

#define NETDRV_DEFAULT_PORT     28474
#define NETDRV_MAGIC_REQUEST    0x4E455444  // "NETD"
#define NETDRV_MAGIC_RESPONSE   0x52455350  // "RESP"
#define NETDRV_MAX_PACKET_SIZE  0x100000    // 1MB max packet

// 网络协议包结构
#pragma pack(push, 1)
typedef struct _NETDRV_PACKET_HEADER {
    DWORD dwMagic;          // 魔数标识
    DWORD dwCommand;        // 命令类型
    DWORD dwDataSize;       // 数据大小
    DWORD dwSequence;       // 序列号
    QWORD qwAddress;        // 物理地址
    DWORD dwLength;         // 读取长度
    DWORD dwFlags;          // 标志位
} NETDRV_PACKET_HEADER, *PNETDRV_PACKET_HEADER;
#pragma pack(pop)

// 命令类型定义
#define NETDRV_CMD_READ_MEMORY      0x01
#define NETDRV_CMD_WRITE_MEMORY     0x02
#define NETDRV_CMD_GET_MEMMAP       0x03
#define NETDRV_CMD_PING             0x04

// 设备上下文结构
typedef struct _DEVICE_CONTEXT_NETDRV {
    SOCKET sock;
    struct sockaddr_in serverAddr;
    CHAR szServerIP[64];
    WORD wPort;
    DWORD dwSequence;
    CRITICAL_SECTION csLock;
    BOOL fConnected;
} DEVICE_CONTEXT_NETDRV, *PDEVICE_CONTEXT_NETDRV;

//-----------------------------------------------------------------------------
// NETWORK COMMUNICATION FUNCTIONS
//-----------------------------------------------------------------------------

/*
* 发送网络包到远程驱动
*/
_Success_(return)
BOOL DeviceNetDrv_SendPacket(_In_ PLC_CONTEXT ctxLC, _In_ PNETDRV_PACKET_HEADER pHeader, _In_reads_opt_(cbData) PBYTE pbData, _In_ DWORD cbData)
{
    PDEVICE_CONTEXT_NETDRV ctx = (PDEVICE_CONTEXT_NETDRV)ctxLC->hDevice;
    DWORD cbSent = 0, cbTotal;
    PBYTE pbBuffer = NULL;
    BOOL fResult = FALSE;
    
    if(!ctx || !ctx->fConnected) { return FALSE; }
    
    // 准备发送缓冲区
    cbTotal = sizeof(NETDRV_PACKET_HEADER) + cbData;
    pbBuffer = LocalAlloc(LMEM_ZEROINIT, cbTotal);
    if(!pbBuffer) { return FALSE; }
    
    // 复制头部和数据
    memcpy(pbBuffer, pHeader, sizeof(NETDRV_PACKET_HEADER));
    if(pbData && cbData) {
        memcpy(pbBuffer + sizeof(NETDRV_PACKET_HEADER), pbData, cbData);
    }
    
    EnterCriticalSection(&ctx->csLock);
    
    // 发送数据
    cbSent = send(ctx->sock, (char*)pbBuffer, cbTotal, 0);
    fResult = (cbSent == cbTotal);
    
    if(!fResult) {
        lcprintf(ctxLC, "NETDRV: Failed to send packet. Error: %d\n", 
#ifdef _WIN32
            WSAGetLastError()
#else
            errno
#endif
        );
    }
    
    LeaveCriticalSection(&ctx->csLock);
    LocalFree(pbBuffer);
    return fResult;
}

/*
* 接收网络包响应
*/
_Success_(return)
BOOL DeviceNetDrv_ReceivePacket(_In_ PLC_CONTEXT ctxLC, _Out_ PNETDRV_PACKET_HEADER pHeader, _Out_opt_ PBYTE* ppbData, _Out_opt_ PDWORD pcbData)
{
    PDEVICE_CONTEXT_NETDRV ctx = (PDEVICE_CONTEXT_NETDRV)ctxLC->hDevice;
    DWORD cbReceived = 0;
    BOOL fResult = FALSE;
    
    if(!ctx || !ctx->fConnected) { return FALSE; }
    if(ppbData) { *ppbData = NULL; }
    if(pcbData) { *pcbData = 0; }
    
    EnterCriticalSection(&ctx->csLock);
    
    // 接收头部
    cbReceived = recv(ctx->sock, (char*)pHeader, sizeof(NETDRV_PACKET_HEADER), MSG_WAITALL);
    if(cbReceived != sizeof(NETDRV_PACKET_HEADER)) {
        lcprintf(ctxLC, "NETDRV: Failed to receive header. Received: %d\n", cbReceived);
        goto cleanup;
    }
    
    // 验证魔数
    if(pHeader->dwMagic != NETDRV_MAGIC_RESPONSE) {
        lcprintf(ctxLC, "NETDRV: Invalid response magic: 0x%08x\n", pHeader->dwMagic);
        goto cleanup;
    }
    
    // 接收数据部分（如果有）
    if(pHeader->dwDataSize > 0) {
        if(pHeader->dwDataSize > NETDRV_MAX_PACKET_SIZE) {
            lcprintf(ctxLC, "NETDRV: Response data too large: %d\n", pHeader->dwDataSize);
            goto cleanup;
        }
        
        if(ppbData) {
            *ppbData = LocalAlloc(LMEM_ZEROINIT, pHeader->dwDataSize);
            if(!*ppbData) { goto cleanup; }
            
            cbReceived = recv(ctx->sock, (char*)*ppbData, pHeader->dwDataSize, MSG_WAITALL);
            if(cbReceived != pHeader->dwDataSize) {
                lcprintf(ctxLC, "NETDRV: Failed to receive data. Expected: %d, Received: %d\n", 
                    pHeader->dwDataSize, cbReceived);
                LocalFree(*ppbData);
                *ppbData = NULL;
                goto cleanup;
            }
            
            if(pcbData) { *pcbData = pHeader->dwDataSize; }
        }
    }
    
    fResult = TRUE;
    
cleanup:
    LeaveCriticalSection(&ctx->csLock);
    return fResult;
}

/*
* 读取单个内存块
*/
_Success_(return)
BOOL DeviceNetDrv_ReadMemory(_In_ PLC_CONTEXT ctxLC, _In_ QWORD qwAddress, _In_ DWORD cbLength, _Out_writes_(cbLength) PBYTE pbBuffer)
{
    NETDRV_PACKET_HEADER reqHeader = {0}, respHeader = {0};
    PDEVICE_CONTEXT_NETDRV ctx = (PDEVICE_CONTEXT_NETDRV)ctxLC->hDevice;
    PBYTE pbResponseData = NULL;
    DWORD cbResponseData = 0;
    BOOL fResult = FALSE;
    
    if(!ctx || !pbBuffer || !cbLength) { return FALSE; }
    
    // 准备请求包
    reqHeader.dwMagic = NETDRV_MAGIC_REQUEST;
    reqHeader.dwCommand = NETDRV_CMD_READ_MEMORY;
    reqHeader.dwDataSize = 0;
    reqHeader.dwSequence = InterlockedIncrement(&ctx->dwSequence);
    reqHeader.qwAddress = qwAddress;
    reqHeader.dwLength = cbLength;
    reqHeader.dwFlags = 0;
    
    // 发送请求
    if(!DeviceNetDrv_SendPacket(ctxLC, &reqHeader, NULL, 0)) {
        return FALSE;
    }
    
    // 接收响应
    if(!DeviceNetDrv_ReceivePacket(ctxLC, &respHeader, &pbResponseData, &cbResponseData)) {
        return FALSE;
    }
    
    // 验证响应
    if(respHeader.dwSequence != reqHeader.dwSequence) {
        lcprintf(ctxLC, "NETDRV: Sequence mismatch. Expected: %d, Got: %d\n", 
            reqHeader.dwSequence, respHeader.dwSequence);
        goto cleanup;
    }
    
    if(cbResponseData != cbLength) {
        lcprintf(ctxLC, "NETDRV: Data size mismatch. Expected: %d, Got: %d\n", 
            cbLength, cbResponseData);
        goto cleanup;
    }
    
    // 复制数据
    memcpy(pbBuffer, pbResponseData, cbLength);
    fResult = TRUE;
    
cleanup:
    if(pbResponseData) {
        LocalFree(pbResponseData);
    }
    return fResult;
}

//-----------------------------------------------------------------------------
// LEECHCORE DEVICE INTERFACE FUNCTIONS
//-----------------------------------------------------------------------------

/*
* 核心scatter读取函数 - 这是LeechCore调用的主要接口
* 这个函数会被VmmScatter_Read最终调用到
*/
VOID DeviceNetDrv_ReadScatter(_In_ PLC_CONTEXT ctxLC, _In_ DWORD cpMEMs, _Inout_ PPMEM_SCATTER ppMEMs)
{
    PDEVICE_CONTEXT_NETDRV ctx = (PDEVICE_CONTEXT_NETDRV)ctxLC->hDevice;
    PMEM_SCATTER pMEM;
    DWORD i;

    if(!ctx || !ctx->fConnected) { return; }

    // 遍历所有内存请求
    for(i = 0; i < cpMEMs; i++) {
        pMEM = ppMEMs[i];
        if(pMEM->f || MEM_SCATTER_ADDR_ISINVALID(pMEM)) {
            continue; // 跳过已完成或无效的请求
        }

        // 通过网络读取内存
        pMEM->f = DeviceNetDrv_ReadMemory(ctxLC, pMEM->qwA, pMEM->cb, pMEM->pb);

        if(pMEM->f && ctxLC->fPrintf[LC_PRINTF_VVV]) {
            lcprintf_fn(ctxLC,
                "NETDRV READ SUCCESS:\n        address=%016llx length=%08x\n",
                pMEM->qwA, pMEM->cb);
        } else if(!pMEM->f) {
            lcprintfvvv_fn(ctxLC,
                "NETDRV READ FAILED:\n        address=%016llx length=%08x\n",
                pMEM->qwA, pMEM->cb);
        }
    }
}

/*
* 写入scatter函数（可选实现）
*/
VOID DeviceNetDrv_WriteScatter(_In_ PLC_CONTEXT ctxLC, _In_ DWORD cpMEMs, _Inout_ PPMEM_SCATTER ppMEMs)
{
    // TODO: 如果需要写入功能，可以在这里实现
    // 类似于读取，但发送NETDRV_CMD_WRITE_MEMORY命令
    UNREFERENCED_PARAMETER(ctxLC);
    UNREFERENCED_PARAMETER(cpMEMs);
    UNREFERENCED_PARAMETER(ppMEMs);
}

/*
* 建立网络连接
*/
_Success_(return)
BOOL DeviceNetDrv_Connect(_In_ PLC_CONTEXT ctxLC)
{
    PDEVICE_CONTEXT_NETDRV ctx = (PDEVICE_CONTEXT_NETDRV)ctxLC->hDevice;
    NETDRV_PACKET_HEADER pingHeader = {0}, respHeader = {0};

#ifdef _WIN32
    WSADATA wsaData;
    if(WSAStartup(MAKEWORD(2,2), &wsaData) != 0) {
        lcprintf(ctxLC, "NETDRV: WSAStartup failed\n");
        return FALSE;
    }
#endif

    // 创建socket
    ctx->sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if(ctx->sock == INVALID_SOCKET) {
        lcprintf(ctxLC, "NETDRV: Failed to create socket\n");
        return FALSE;
    }

    // 设置服务器地址
    ctx->serverAddr.sin_family = AF_INET;
    ctx->serverAddr.sin_port = htons(ctx->wPort);
    inet_pton(AF_INET, ctx->szServerIP, &ctx->serverAddr.sin_addr);

    // 连接到服务器
    if(connect(ctx->sock, (struct sockaddr*)&ctx->serverAddr, sizeof(ctx->serverAddr)) != 0) {
        lcprintf(ctxLC, "NETDRV: Failed to connect to %s:%d\n", ctx->szServerIP, ctx->wPort);
#ifdef _WIN32
        closesocket(ctx->sock);
#else
        close(ctx->sock);
#endif
        return FALSE;
    }

    ctx->fConnected = TRUE;

    // 发送ping测试连接
    pingHeader.dwMagic = NETDRV_MAGIC_REQUEST;
    pingHeader.dwCommand = NETDRV_CMD_PING;
    pingHeader.dwSequence = InterlockedIncrement(&ctx->dwSequence);

    if(!DeviceNetDrv_SendPacket(ctxLC, &pingHeader, NULL, 0) ||
       !DeviceNetDrv_ReceivePacket(ctxLC, &respHeader, NULL, NULL)) {
        lcprintf(ctxLC, "NETDRV: Ping test failed\n");
        ctx->fConnected = FALSE;
        return FALSE;
    }

    lcprintf(ctxLC, "NETDRV: Successfully connected to %s:%d\n", ctx->szServerIP, ctx->wPort);
    return TRUE;
}

/*
* 关闭设备连接
*/
VOID DeviceNetDrv_Close(_Inout_ PLC_CONTEXT ctxLC)
{
    PDEVICE_CONTEXT_NETDRV ctx = (PDEVICE_CONTEXT_NETDRV)ctxLC->hDevice;
    if(ctx) {
        if(ctx->fConnected && ctx->sock != INVALID_SOCKET) {
#ifdef _WIN32
            closesocket(ctx->sock);
            WSACleanup();
#else
            close(ctx->sock);
#endif
        }
        DeleteCriticalSection(&ctx->csLock);
        LocalFree(ctx);
        ctxLC->hDevice = 0;
    }
}

/*
* 获取设备选项
*/
_Success_(return)
BOOL DeviceNetDrv_GetOption(_In_ PLC_CONTEXT ctxLC, _In_ QWORD fOption, _Out_ PQWORD pqwValue)
{
    UNREFERENCED_PARAMETER(ctxLC);

    switch(fOption) {
        case LC_OPT_MEMORYINFO_VALID:
            *pqwValue = 1;  // 表示内存信息有效
            return TRUE;
        default:
            return FALSE;
    }
}

/*
* 解析设备参数
*/
VOID DeviceNetDrv_ParseParameters(_In_ PLC_CONTEXT ctxLC, _Inout_ PDEVICE_CONTEXT_NETDRV ctx)
{
    PLC_DEVICE_PARAMETER_ENTRY pParam;

    // 默认值
    strcpy_s(ctx->szServerIP, sizeof(ctx->szServerIP), "127.0.0.1");
    ctx->wPort = NETDRV_DEFAULT_PORT;

    // 解析IP地址参数
    if((pParam = LcDeviceParameterGet(ctxLC, "ip")) && pParam->szValue[0]) {
        strcpy_s(ctx->szServerIP, sizeof(ctx->szServerIP), pParam->szValue);
    }

    // 解析端口参数
    if((pParam = LcDeviceParameterGet(ctxLC, "port")) && pParam->szValue[0]) {
        ctx->wPort = (WORD)strtoul(pParam->szValue, NULL, 10);
        if(ctx->wPort == 0) {
            ctx->wPort = NETDRV_DEFAULT_PORT;
        }
    }

    // 也可以从设备字符串中解析，格式: netdrv://ip:port
    if(strstr(ctxLC->Config.szDevice, "://")) {
        CHAR szTemp[256] = {0};
        LPSTR szIP, szPort;

        strcpy_s(szTemp, sizeof(szTemp), ctxLC->Config.szDevice);
        szIP = strstr(szTemp, "://");
        if(szIP) {
            szIP += 3; // 跳过 "://"
            szPort = strchr(szIP, ':');
            if(szPort) {
                *szPort = 0;
                szPort++;
                ctx->wPort = (WORD)strtoul(szPort, NULL, 10);
                if(ctx->wPort == 0) {
                    ctx->wPort = NETDRV_DEFAULT_PORT;
                }
            }
            if(strlen(szIP) > 0) {
                strcpy_s(ctx->szServerIP, sizeof(ctx->szServerIP), szIP);
            }
        }
    }
}

/*
* 主设备打开函数 - 这是LeechCore调用的入口点
*/
_Success_(return)
BOOL DeviceNetDrv_Open(_Inout_ PLC_CONTEXT ctxLC, _Out_opt_ PPLC_CONFIG_ERRORINFO ppLcCreateErrorInfo)
{
    PDEVICE_CONTEXT_NETDRV ctx;

    if(ppLcCreateErrorInfo) { *ppLcCreateErrorInfo = NULL; }

    // 分配设备上下文
    ctx = (PDEVICE_CONTEXT_NETDRV)LocalAlloc(LMEM_ZEROINIT, sizeof(DEVICE_CONTEXT_NETDRV));
    if(!ctx) {
        lcprintf(ctxLC, "NETDRV: Failed to allocate device context\n");
        return FALSE;
    }

    // 初始化上下文
    ctxLC->hDevice = (HANDLE)ctx;
    ctx->sock = INVALID_SOCKET;
    ctx->dwSequence = 0;
    ctx->fConnected = FALSE;
    InitializeCriticalSection(&ctx->csLock);

    // 解析参数
    DeviceNetDrv_ParseParameters(ctxLC, ctx);

    // 设置LeechCore回调函数
    ctxLC->Config.fVolatile = TRUE;  // 内存是易失性的（实时）
    ctxLC->pfnClose = DeviceNetDrv_Close;
    ctxLC->pfnReadScatter = DeviceNetDrv_ReadScatter;
    ctxLC->pfnWriteScatter = DeviceNetDrv_WriteScatter;  // 可选
    ctxLC->pfnGetOption = DeviceNetDrv_GetOption;

    // 建立网络连接
    if(!DeviceNetDrv_Connect(ctxLC)) {
        lcprintf(ctxLC, "NETDRV: Failed to connect to remote driver\n");
        DeviceNetDrv_Close(ctxLC);
        return FALSE;
    }

    // 设置默认内存映射（如果远程驱动不提供的话）
    // 这里可以添加获取远程内存映射的代码
    if(!LcMemMap_AddRange(ctxLC, 0x0000000000000000ULL, 0x0000100000000000ULL, 0x0000000000000000ULL)) {
        lcprintf(ctxLC, "NETDRV: Failed to set memory map\n");
        DeviceNetDrv_Close(ctxLC);
        return FALSE;
    }

    lcprintf(ctxLC, "NETDRV: Successfully initialized network driver device\n");
    return TRUE;
}
