﻿# VMM 静态库使用指南

## 构建要求

使用 Visual Studio 2019 或更高版本构建静态库：

```bash
# 构建 LeechCore 静态库
cd LeechCore
msbuild leechcore.sln /p:Configuration=Release /p:Platform=x64

# 构建 VMM 静态库
cd MemProcFS\vmm
msbuild vmm.vcxproj /p:Configuration=Release /p:Platform=x64
```

## Visual Studio 项目配置

### 包含目录
```
MemProcFS\includes
MemProcFS\vmm
```

### 库目录
```
MemProcFS\includes\lib64    # x64 构建
MemProcFS\includes\lib32    # x86 构建
MemProcFS\includes\libarm64 # ARM64 构建
```

### 预处理器定义
```cpp
#define VMM_STATIC
#define LEECHCORE_STATIC
```

### 链接库
```
vmm.lib
leechcore.lib
bcrypt.lib
crypt32.lib
shlwapi.lib
ws2_32.lib
rpcrt4.lib
secur32.lib
credui.lib
setupapi.lib
winusb.lib
legacy_stdio_definitions.lib
oldnames.lib
```

### 运行时库设置
- 使用 `多线程 (/MT)` 用于 Release
- 使用 `多线程调试 (/MTd)` 用于 Debug

## 代码使用

### 包含头文件
```cpp
#define VMM_STATIC
#define LEECHCORE_STATIC

#include "vmmdll.h"
#include "leechcore.h"
```

### 初始化
```cpp
// 1. 初始化 LeechCore
if (!LeechCore_StaticInitialize()) {
    printf("Failed to initialize LeechCore\n");
    return -1;
}

// 2. 初始化 VMM
if (!VMMDLL_StaticInitialize()) {
    printf("Failed to initialize VMM\n");
    LeechCore_StaticCleanup();
    return -1;
}

// 3. 创建 VMM 句柄
VMM_HANDLE hVMM = VMMDLL_Initialize(argc, argv);
if (!hVMM) {
    printf("Failed to create VMM handle\n");
    VMMDLL_StaticCleanup();
    LeechCore_StaticCleanup();
    return -1;
}
```

### 清理
```cpp
// 按相反顺序清理
if (hVMM) {
    VMMDLL_Close(hVMM);
}
VMMDLL_StaticCleanup();
LeechCore_StaticCleanup();
```

## 完整示例

```cpp
#define VMM_STATIC
#define LEECHCORE_STATIC

#include "vmmdll.h"
#include "leechcore.h"
#include <stdio.h>

int main(int argc, char* argv[]) {
    // 初始化
    if (!LeechCore_StaticInitialize()) {
        printf("Failed to initialize LeechCore\n");
        return -1;
    }

    if (!VMMDLL_StaticInitialize()) {
        printf("Failed to initialize VMM\n");
        LeechCore_StaticCleanup();
        return -1;
    }

    // 创建句柄
    VMM_HANDLE hVMM = VMMDLL_Initialize(argc, argv);
    if (!hVMM) {
        printf("Failed to create VMM handle\n");
        VMMDLL_StaticCleanup();
        LeechCore_StaticCleanup();
        return -1;
    }

    // 使用 VMM 功能
    VMMDLL_SYSTEM_INFORMATION sysInfo;
    if (VMMDLL_WinGetSystemInformation(hVMM, &sysInfo)) {
        printf("OS Version: %i.%i.%i\n", 
               sysInfo.dwVersionMajor, 
               sysInfo.dwVersionMinor, 
               sysInfo.dwVersionBuild);
    }

    // 清理
    VMMDLL_Close(hVMM);
    VMMDLL_StaticCleanup();
    LeechCore_StaticCleanup();
    
    return 0;
}
```

## 项目属性快速配置

**C/C++:**
- 预处理器定义: `VMM_STATIC;LEECHCORE_STATIC`
- 代码生成 - 运行库: `多线程 (/MT)`

**链接器:**
- 附加库目录: `包含 vmm.lib 和 leechcore.lib 的路径`
- 附加依赖项: `上述所有库列表`
- 忽略特定默认库: `MSVCRT.lib` (如果使用 /MT)

## 注意事项

1. 必须先初始化 LeechCore，再初始化 VMM
2. 清理顺序与初始化相反
3. 确保所有库使用相同的运行时库设置
4. 确保所有库都是相同平台构建 (x64/x86/ARM64)
