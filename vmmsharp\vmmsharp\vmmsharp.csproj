<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFrameworks>net48;net5.0;net6.0;net7.0;net8.0</TargetFrameworks>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <OutputType>Library</OutputType>
    <RootNamespace>Vmmsharp</RootNamespace>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <OutputPath>..\files\temp\vmmsharp\</OutputPath>
    <AssemblyTitle>MemProcFS : C# API</AssemblyTitle>
    <Company>Ulf Frisk</Company>
    <Product>vmmsharp</Product>
    <Copyright>Copyright © Ulf Frisk 2024</Copyright>
    <AssemblyVersion>3.3.0.7</AssemblyVersion>
    <FileVersion>3.3.0.7</FileVersion>
    <Platforms>x64</Platforms>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
    <PlatformTarget>x64</PlatformTarget>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net48|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net5.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net6.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net7.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Debug|net8.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net48|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net5.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net6.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net7.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net8.0|x64'">
    <DebugType>full</DebugType>
    <NoWarn>CS0649;1701;1702</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
  </ItemGroup>
    <ItemGroup>
        <Content Include="..\..\LICENSE">
            <Pack>true</Pack>
            <PackagePath></PackagePath>
            <Visible>True</Visible>
        </Content>
    </ItemGroup>
    <ItemGroup>
        <Content Include="..\README.md">
            <Pack>true</Pack>
            <PackagePath></PackagePath>
            <Visible>True</Visible>
        </Content>
    </ItemGroup>
	<ItemGroup>
		<None Include="logo.png" Pack="true" Visible="true" PackagePath="" />
	</ItemGroup>
    <PropertyGroup>
        <Version>5.14.10</Version>
        <RepositoryUrl>https://github.com/ufrisk/MemProcFS</RepositoryUrl>
        <RepositoryType>git</RepositoryType>
        <PackageLicenseFile>LICENSE</PackageLicenseFile>
        <PackageReadmeFile>README.md</PackageReadmeFile>
        <PackageId>Vmmsharp</PackageId>
        <IncludeContentInPack>true</IncludeContentInPack>
        <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<PackageIcon>logo.png</PackageIcon>
		<Description>Vmmsharp is a C# library for the MemProcFS memory analysis framework.</Description>		
    </PropertyGroup>	
</Project>
