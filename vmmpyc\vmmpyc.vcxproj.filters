﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Header Files\includes">
      <UniqueIdentifier>{bfa25535-d9b8-480a-b50d-50d3a8b46bfb}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\leechcore.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\vmmdll.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="vmmpyc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="vmmpyc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_kernel.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_maps.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_module.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_modulemaps.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_pdb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_process.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_processmaps.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_reghive.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_regkey.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_regvalue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_vfs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_vmm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpycplugin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_physicalmemory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_regmemory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_virtualmemory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="oscompatibility.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_scattermemory.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_virtualmachine.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_search.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmpyc_yara.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="vmmpyc.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>