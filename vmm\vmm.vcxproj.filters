﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Source Files\modules">
      <UniqueIdentifier>{00c5c579-6475-46c5-8997-359b6c6649b3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\ob">
      <UniqueIdentifier>{4e91bd71-3b0c-4f6d-9e2c-306e6f275360}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\mm">
      <UniqueIdentifier>{338e412d-8a2d-4a56-9f79-32aa6091c271}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\includes">
      <UniqueIdentifier>{793ab526-24d7-44c9-b5d8-b41a0c1a0ab2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ob">
      <UniqueIdentifier>{4b033bbd-b98a-426a-b25c-4c7d5686604e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files\res">
      <UniqueIdentifier>{36acfda3-7a80-4829-affe-6ab4d6ef5c9f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\mm">
      <UniqueIdentifier>{e6b89b0f-2ea4-4de1-b26d-49376653099f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\modules">
      <UniqueIdentifier>{205ba541-835d-4dc2-a488-6113fe0438d1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\ext">
      <UniqueIdentifier>{b1407620-53ff-4f04-8091-ec40de1b9a09}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\ext">
      <UniqueIdentifier>{322652a4-b100-439f-a35d-a04ac22c8e0a}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="vmmdll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="statistics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmproc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pluginmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pe.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmwin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmwininit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="version.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmwinreg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pdb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="sysquery.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmwindef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmwinobj.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\leechcore.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="fc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmnet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ob\ob.h">
      <Filter>Header Files\ob</Filter>
    </ClInclude>
    <ClInclude Include="vmmwinsvc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="charutil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="oscompatibility.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="infodb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmwinpool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmlog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmheap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmwork.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmvm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\libpdbcrust.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="mm\mm.h">
      <Filter>Header Files\mm</Filter>
    </ClInclude>
    <ClInclude Include="mm\mm_pfn.h">
      <Filter>Header Files\mm</Filter>
    </ClInclude>
    <ClInclude Include="modules\modules.h">
      <Filter>Header Files\modules</Filter>
    </ClInclude>
    <ClInclude Include="modules\modules_init.h">
      <Filter>Header Files\modules</Filter>
    </ClInclude>
    <ClInclude Include="vmmdll_core.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\includes\vmmyara.h">
      <Filter>Header Files\includes</Filter>
    </ClInclude>
    <ClInclude Include="vmmyarautil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ext\miniz.h">
      <Filter>Header Files\ext</Filter>
    </ClInclude>
    <ClInclude Include="ext\sqlite3.h">
      <Filter>Header Files\ext</Filter>
    </ClInclude>
    <ClInclude Include="ext\sqlite3ext.h">
      <Filter>Header Files\ext</Filter>
    </ClInclude>
    <ClInclude Include="vmmdll_remote.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="vmmuserconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ob\ob_tag.h">
      <Filter>Source Files\ob</Filter>
    </ClInclude>
    <ClInclude Include="ext\sha256.h">
      <Filter>Header Files\ext</Filter>
    </ClInclude>
    <ClInclude Include="vmmwinthread.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ext\lz4.h">
      <Filter>Header Files\ext</Filter>
    </ClInclude>
    <ClInclude Include="vmmex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="vmmdll.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="statistics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmproc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pluginmanager.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pe.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmwin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmwininit.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmwinreg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pdb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sysquery.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmwinobj.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmnet.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_container.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_core.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_map.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_set.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="vmmwinsvc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_cachemap.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_strmap.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_compressed.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_memfile.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="charutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="oscompatibility.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="infodb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmwinpool.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_counter.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="vmmdll_scatter.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmlog.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmheap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmdll_core.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmwork.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmvm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mm\mm_pfn.c">
      <Filter>Source Files\mm</Filter>
    </ClCompile>
    <ClCompile Include="mm\mm_vad.c">
      <Filter>Source Files\mm</Filter>
    </ClCompile>
    <ClCompile Include="mm\mm_win.c">
      <Filter>Source Files\mm</Filter>
    </ClCompile>
    <ClCompile Include="mm\mm_x64.c">
      <Filter>Source Files\mm</Filter>
    </ClCompile>
    <ClCompile Include="mm\mm_x86.c">
      <Filter>Source Files\mm</Filter>
    </ClCompile>
    <ClCompile Include="mm\mm_x86pae.c">
      <Filter>Source Files\mm</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_conf.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_csv.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_handle.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_json.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_module.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_ntfs.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_proc.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_registry.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_sys.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_thread.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_timeline.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_misc_bitlocker.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_misc_procinfo.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_misc_view.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_phys2virt.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_file_handles_vads.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_file_modules.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_handle.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_heap.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_ldrmodules.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_memmap.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_minidump.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_thread.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_token.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_search.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_cert.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_driver.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_mem.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_net.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_obj.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_pool.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_proc.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_svc.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_syscall.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_task.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_user.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_vfsfc.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_vfsproc.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_vfsroot.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_vm.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_winreg.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="vmmyarautil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="vmmyarawrap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_kern1.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_proc1.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_proc2.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_proc3.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_thread1.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_findevil.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_yara.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_searchyara.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_file.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="vmmuserconfig.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ext\miniz.c">
      <Filter>Source Files\ext</Filter>
    </ClCompile>
    <ClCompile Include="ext\sqlite3.c">
      <Filter>Source Files\ext</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_kernproc1.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="vmmdll_remote.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mm\mm_arm64.c">
      <Filter>Source Files\mm</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_virt2phys.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="ob\ob_bytequeue.c">
      <Filter>Source Files\ob</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_web.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_av1.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="ext\sha256.c">
      <Filter>Source Files\ext</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_sysinfo.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_misc_eventlog.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_apc1.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_fc_prefetch.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_proc_console.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="vmmwinthread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ext\lz4.c">
      <Filter>Source Files\ext</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_evil_entropy.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="modules\m_sys_netdns.c">
      <Filter>Source Files\modules</Filter>
    </ClCompile>
    <ClCompile Include="vmmex_light.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="vmmdll.def">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\m_fc_json_elastic_import.ps1">
      <Filter>Resource Files\res</Filter>
    </None>
    <None Include="Makefile">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\MemProcFS-dev\vmm\res\m_fc_json_elastic_import_unauth.ps1">
      <Filter>Resource Files\res</Filter>
    </None>
    <None Include="Makefile.macos">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="vmm.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>